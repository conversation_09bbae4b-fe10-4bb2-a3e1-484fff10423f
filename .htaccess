   # Allow direct access to PHP files (no redirect)
   RewriteCond %{REQUEST_URI} \.php$
   RewriteRule ^ - [L]

# Enable URL rewriting
RewriteEngine On

# SMART ENVIRONMENT DETECTION: Auto-detect localhost vs production
# For localhost (XAMPP) - use /helloit/ base
RewriteCond %{HTTP_HOST} ^localhost [NC,OR]
RewriteCond %{HTTP_HOST} ^127\.0\.0\.1 [NC]
RewriteRule ^(.*)$ - [E=LOCALHOST:1]

# Set the base directory for localhost
RewriteCond %{ENV:LOCALHOST} ^1$
RewriteRule .* - [E=REWRITE_BASE:/helloit/]

# Set the base directory for production
RewriteCond %{ENV:LOCALHOST} !^1$
RewriteRule .* - [E=REWRITE_BASE:/]

# Remove trailing slashes (environment-aware)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{ENV:LOCALHOST} ^1$
RewriteRule ^(.*)/$ /helloit/$1 [L,R=301]

RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{ENV:LOCALHOST} !^1$
RewriteRule ^(.*)/$ /$1 [L,R=301]

# Redirect the root to index.php without showing index.php in the URL
RewriteRule ^$ index.php [L]

# Redirect index.php to the clean URL (environment-aware)
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/index\.php [NC]
RewriteCond %{ENV:LOCALHOST} ^1$
RewriteRule ^ /helloit/ [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+index\.php [NC]
RewriteCond %{ENV:LOCALHOST} !^1$
RewriteRule ^ / [R=301,L]



# Handle specific page redirects with clean URLs for support-ticket section
RewriteRule ^support-ticket/buy-now/?$ front-end/buy-now.php [L]
RewriteRule ^support-ticket/issues-we-solve/?$ front-end/issues-we-solve.php [L]
RewriteRule ^support-ticket/faq/?$ front-end/faq.php [L]
RewriteRule ^support-ticket/about-us/?$ front-end/about-us.php [L]
RewriteRule ^support-ticket/contact/?$ front-end/contact.php [L]
RewriteRule ^support-ticket/send-contact/?$ front-end/send-contact.php [L]
RewriteRule ^support-ticket/sign-in/?$ front-end/sign-in.php [L]
RewriteRule ^support-ticket/sign-up/?$ front-end/sign-up.php [L]
RewriteRule ^support-ticket/cart/?$ front-end/cart.php [L]
RewriteRule ^support-ticket/cart-add/?$ front-end/cart-add.php [L]
RewriteRule ^support-ticket/cart-remove/?$ front-end/cart-remove.php [L]
RewriteRule ^support-ticket/get-messages/?$ front-end/get-messages.php [L]
RewriteRule ^support-ticket/send-message/?$ front-end/send-message.php [L]
RewriteRule ^support-ticket/check-notifications/?$ front-end/check-notifications.php [L]
RewriteRule ^support-ticket/select-payment-method/?$ front-end/select-payment-method.php [L]
RewriteRule ^support-ticket/webhook/?$ front-end/stripe-webhook.php [L]

# Redirect specific PHP files to their clean URL versions with support-ticket prefix
# Environment-aware redirects using REWRITE_BASE
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/buy-now\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/buy-now [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/issues-we-solve\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/issues-we-solve [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/faq\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/faq [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/about-us\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/about-us [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/contact\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/contact [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/send-contact\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/send-contact [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/sign-in\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/sign-in [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/sign-up\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/sign-up [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/cart\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/cart [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/get-messages\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/get-messages [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/send-message\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/send-message [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/front-end/check-notifications\.php [NC]
RewriteRule ^ %{ENV:REWRITE_BASE}support-ticket/check-notifications [R=301,L]

# General rule to remove .php extension (excluding function and database files)
RewriteCond %{THE_REQUEST} \s/+(helloit/.+?)\.php[\s?] [NC]
RewriteCond %{REQUEST_URI} !^/helloit/(functions|database|includes|config)/ [NC]
RewriteRule ^ /%1 [R=301,NE,L]

# Internally map requests without .php to the PHP files (excluding function and database files)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteCond %{REQUEST_URI} !^/helloit/(functions|database|includes|config)/ [NC]
RewriteRule ^(.+?)/?$ $1.php [L]

# Disable directory indexes
Options -Indexes

# Explicitly set DirectoryIndex
DirectoryIndex index.php

# Handle 404 errors (environment-aware)
# Note: ErrorDocument doesn't support environment variables, so we use a fallback approach
ErrorDocument 404 /404.php

# Set default character set
AddDefaultCharset UTF-8

# Enable browser caching for better performance
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType application/pdf "access plus 1 month"
  ExpiresByType text/x-javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
  ExpiresByType application/x-javascript "access plus 1 month"
  ExpiresByType application/x-shockwave-flash "access plus 1 month"
  ExpiresByType image/x-icon "access plus 1 year"
  ExpiresDefault "access plus 2 days"
</IfModule>

# Compress text files for faster loading
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/x-javascript text/javascript
</IfModule>

# Protect sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to .htaccess file
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Prevent access to sensitive directories
RedirectMatch 403 ^/helloit/(includes|config|logs|functions|database)/?$

RewriteRule ^merlion/admin-login/?$ merlion/admin-login.php [L]
RewriteRule ^merlion/admin-logout/?$ merlion/admin-logout.php [L]

# Additional merlion folder rules
RewriteRule ^merlion/index/?$ merlion/index.php [L]
RewriteRule ^merlion/admin-tickets/?$ merlion/admin-tickets.php [L]
RewriteRule ^merlion/admin-ticket-logs/?$ merlion/admin-ticket-logs.php [L]
RewriteRule ^merlion/admin-chat/?$ merlion/admin-chat.php [L]
RewriteRule ^merlion/get-messages/?$ merlion/get-messages.php [L]
RewriteRule ^merlion/send-message/?$ merlion/send-message.php [L]
RewriteRule ^merlion/admin-purchases/?$ merlion/admin-purchases.php [L]
RewriteRule ^merlion/admin-users/?$ merlion/admin-users.php [L]
RewriteRule ^merlion/admin-staff/?$ merlion/admin-staff.php [L]
RewriteRule ^merlion/api-key-manager/?$ merlion/api-key-manager.php [L]

# Redirect specific PHP files to their clean URL versions for merlion folder
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/index\.php [NC]
RewriteRule ^ /helloit/merlion/index [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/admin-tickets\.php [NC]
RewriteRule ^ /helloit/merlion/admin-tickets [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/admin-ticket-logs\.php [NC]
RewriteRule ^ /helloit/merlion/admin-ticket-logs [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/admin-chat\.php [NC]
RewriteRule ^ /helloit/merlion/admin-chat [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/get-messages\.php [NC]
RewriteRule ^ /helloit/merlion/get-messages [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/send-message\.php [NC]
RewriteRule ^ /helloit/merlion/send-message [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/admin-purchases\.php [NC]
RewriteRule ^ /helloit/merlion/admin-purchases [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/admin-users\.php [NC]
RewriteRule ^ /helloit/merlion/admin-users [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/admin-staff\.php [NC]
RewriteRule ^ /helloit/merlion/admin-staff [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+helloit/merlion/api-key-manager\.php [NC]
RewriteRule ^ /helloit/merlion/api-key-manager [R=301,L]

# Handle notification endpoints
RewriteRule ^merlion/check-notifications/?$ merlion/check-notifications.php [L]
RewriteRule ^support-ticket/check-notifications/?$ front-end/check-notifications.php [L]