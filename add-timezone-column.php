<?php
/**
 * Add Timezone Column to User Table
 * Fixes the "Unknown column 'timezone'" error
 */

// Include the database connection
include('functions/server.php');

echo "<h1>🔧 Add Timezone Column to User Table</h1>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>⚠️ Fixing Database Schema</h2>";
echo "<p><strong>Error:</strong> Unknown column 'timezone' in 'field list'</p>";
echo "<p><strong>Solution:</strong> Add timezone column to user table</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🗄️ Current User Table Structure</h2>";

// Check current table structure
try {
    $structure_result = $conn->query("DESCRIBE user");
    
    if ($structure_result) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📊 Current Columns:</h3>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Column</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Type</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Null</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Default</th>";
        echo "</tr>";
        
        $has_timezone = false;
        while ($column = $structure_result->fetch_assoc()) {
            if ($column['Field'] === 'timezone') {
                $has_timezone = true;
            }
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $column['Field'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $column['Type'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $column['Null'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        if ($has_timezone) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>Timezone column already exists!</strong></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Timezone column missing - needs to be added</strong></p>";
            echo "</div>";
        }
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error checking table structure:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🔧 Add Timezone Column</h2>";

// Add timezone column if it doesn't exist
try {
    // Check if timezone column exists
    $check_column = $conn->query("SHOW COLUMNS FROM user LIKE 'timezone'");
    
    if (!$check_column || $check_column->num_rows == 0) {
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>➕ Adding Timezone Column...</h3>";
        
        // Add the timezone column
        $add_column_sql = "ALTER TABLE user ADD COLUMN timezone VARCHAR(50) DEFAULT 'Asia/Singapore' AFTER country";
        
        if ($conn->query($add_column_sql)) {
            echo "<p>✅ <strong>Success!</strong> Timezone column added to user table</p>";
            echo "<p><strong>Column details:</strong></p>";
            echo "<ul>";
            echo "<li><strong>Name:</strong> timezone</li>";
            echo "<li><strong>Type:</strong> VARCHAR(50)</li>";
            echo "<li><strong>Default:</strong> 'Asia/Singapore'</li>";
            echo "<li><strong>Position:</strong> After country column</li>";
            echo "</ul>";
            
            // Verify the column was added
            $verify_result = $conn->query("SHOW COLUMNS FROM user LIKE 'timezone'");
            if ($verify_result && $verify_result->num_rows > 0) {
                $column_info = $verify_result->fetch_assoc();
                echo "<p><strong>Verification:</strong> Column successfully created</p>";
                echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                print_r($column_info);
                echo "</pre>";
            }
        } else {
            echo "<p>❌ <strong>Error adding column:</strong> " . $conn->error . "</p>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ Timezone Column Already Exists</h3>";
        echo "<p>The timezone column is already present in the user table.</p>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error adding timezone column:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🌍 Populate Timezone from Country</h2>";

// Update existing users' timezones based on their country
try {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔄 Auto-Populate Timezones from Countries...</h3>";
    
    // Get users with countries but no timezone
    $users_sql = "SELECT id, username, country, timezone FROM user WHERE country IS NOT NULL AND country != '' AND (timezone IS NULL OR timezone = '')";
    $users_result = $conn->query($users_sql);
    
    if ($users_result && $users_result->num_rows > 0) {
        echo "<p>Found " . $users_result->num_rows . " users with countries but no timezone set.</p>";
        
        // Include the timezone helper to get country mapping
        $country_mapping = UTCTimeHelper::getCountryTimezoneMapping();
        
        $updated_count = 0;
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Username</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Country</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Detected Timezone</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
        echo "</tr>";
        
        while ($user = $users_result->fetch_assoc()) {
            $country = $user['country'];
            $detected_timezone = $country_mapping[$country] ?? null;
            
            if ($detected_timezone) {
                // Update user's timezone
                $update_sql = "UPDATE user SET timezone = ? WHERE id = ?";
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("si", $detected_timezone, $user['id']);
                
                if ($stmt->execute()) {
                    $updated_count++;
                    $status = "✅ Updated";
                } else {
                    $status = "❌ Error: " . $conn->error;
                }
            } else {
                $status = "⚠️ Country not mapped";
            }
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($country) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($detected_timezone ?? 'Not found') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>$status</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "<p><strong>Summary:</strong> Updated $updated_count users with timezone based on their country.</p>";
    } else {
        echo "<p>No users found that need timezone updates.</p>";
    }
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error updating user timezones:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>✅ Verification</h2>";

// Verify the fix
try {
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔍 Final Verification:</h3>";
    
    // Check if timezone column exists now
    $final_check = $conn->query("SHOW COLUMNS FROM user LIKE 'timezone'");
    if ($final_check && $final_check->num_rows > 0) {
        echo "<p>✅ <strong>Timezone column exists</strong></p>";
        
        // Test the function that was causing the error
        $test_sql = "SELECT id, username, country, timezone FROM user LIMIT 3";
        $test_result = $conn->query($test_sql);
        
        if ($test_result) {
            echo "<p>✅ <strong>Database query works</strong></p>";
            echo "<table style='width: 100%; border-collapse: collapse;'>";
            echo "<tr style='background: #e9ecef;'>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>Username</th>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>Country</th>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>Timezone</th>";
            echo "</tr>";
            
            while ($user = $test_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($user['country'] ?? 'NULL') . "</td>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($user['timezone'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p>❌ <strong>Timezone column still missing</strong></p>";
    }
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Verification error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🚀 Next Steps</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Database Fix Complete!</h3>";
echo "<p>The timezone column has been added to your user table. Now you can:</p>";
echo "<ol>";
echo "<li><strong>Test my-ticket-log.php:</strong> Should work without errors</li>";
echo "<li><strong>Check timezone display:</strong> Should show user's country timezone</li>";
echo "<li><strong>Create tickets:</strong> Should save UTC and display local time</li>";
echo "<li><strong>Verify other pages:</strong> All timezone functions should work</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; background: #28a745; color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🎉 DATABASE SCHEMA FIXED!</h2>";
echo "<p><strong>The timezone column has been added successfully!</strong></p>";
echo "<p>Your country-to-timezone system is now ready to use.</p>";
echo "</div>";
?>