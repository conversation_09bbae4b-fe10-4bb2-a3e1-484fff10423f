<?php
include('functions/server.php');

// Check if the stripe_customer_id column exists in the user table
$sql = "SHOW COLUMNS FROM user LIKE 'stripe_customer_id'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    // The column doesn't exist, so add it
    $sql = "ALTER TABLE user ADD COLUMN stripe_customer_id VARCHAR(255) DEFAULT NULL";
    if ($conn->query($sql) === TRUE) {
        echo "<p>Added stripe_customer_id column to user table.</p>";
    } else {
        echo "<p>Error adding stripe_customer_id column: " . $conn->error . "</p>";
    }
} else {
    echo "<p>The stripe_customer_id column already exists in the user table.</p>";
}

$conn->close();
?>
