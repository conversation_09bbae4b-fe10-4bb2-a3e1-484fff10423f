<?php
/**
 * Cleanup Test Files - Remove all test and debug files created during development
 */

echo "<h1>🧹 Test Files Cleanup</h1>";

// List of test files to remove
$test_files_to_remove = [
    // Front-end test files
    'front-end/test-address2-fix.php',
    'front-end/test-autofill-signin.php',
    'front-end/test-back-button-logic.php',
    'front-end/test-cart-limit.php',
    'front-end/test-cart-redirect.php',
    'front-end/test-clipboard-debug.php',
    'front-end/test-complete-purchase-fix.php',
    'front-end/test-correct-purchase-processing.php',
    'front-end/test-credentials-ui.php',
    'front-end/test-double-processing-fix.php',
    'front-end/test-guest-cart.php',
    'front-end/test-localhost-credentials-fix.php',
    'front-end/test-localhost-fix.php',
    'front-end/test-metadata-fix.php',
    'front-end/test-modal-fix.php',
    'front-end/test-password-fields-fix.php',
    'front-end/test-password-fix.php',
    'front-end/test-payment-fixes.php',
    'front-end/test-payment-method-fix.php',
    'front-end/test-payment-success-urls.php',
    'front-end/test-reset-password.php',
    'front-end/test-simple-localhost-fix.php',
    'front-end/test-stripe-api.php',
    'front-end/test-stripe-customer.php',
    'front-end/test-suburb-district-fix.php',
    'front-end/test-suburb-parsing-fix.php',
    'front-end/test-success.php',
    'front-end/test-undefined-variable-fix.php',
    'front-end/test-url-fix.php',
    'front-end/test-webhook.php',
    'front-end/debug-payment.php',
    'front-end/debug-stripe-address.php',
    'front-end/deployment-checklist.php',
    'front-end/cpanel-deployment-readiness.php',
    'front-end/server-deployment-analysis.php',
    'front-end/phpinfo.php',
    'front-end/simulate-webhook.php',
    
    // Functions test files
    'functions/test-all-fixes.php',
    'functions/test-cart-clear-fix.php',
    'functions/test-cart-metadata-fix.php',
    'functions/test-cart-sessions-cleanup.php',
    'functions/test-email-security-fix.php',
    'functions/test-fixed-cart-checkout.php',
    'functions/test-guest-card-saving-final.php',
    'functions/test-guest-card-saving.php',
    'functions/test-guest-checkbox-fix.php',
    'functions/test-guest-signin-fix.php',
    'functions/test-improved-webhook.php',
    'functions/test-logged-in-checkbox-fix.php',
    'functions/test-logged-in-cleanup-fix.php',
    'functions/test-logged-in-cleanup.php',
    'functions/test-optional-fields.php',
    'functions/test-payment-success-fix.php',
    'functions/test-payment-temp-cleanup.php',
    'functions/test-photo-upload.php',
    'functions/test-reset-password-fix.php',
    'functions/test-reset-password-profile.php',
    'functions/test-save-card-checkbox.php',
    'functions/test-signin-step-by-step.php',
    'functions/test-user95404-password.php',
    'functions/test-username-editing.php',
    'functions/test-webhook-fix.php',
    'functions/debug-cart-purchase-issue.php',
    'functions/debug-payment-success.php',
    'functions/debug-signin-issue.php',
    'functions/debug-user95404.php',
    'functions/debug-webhook-metadata.php',
    'functions/quick-debug.php',
    'functions/password-diagnostic.php',
    'functions/comprehensive-password-fix.php',
    'functions/dual-environment-setup.php',
    'functions/fix-missing-purchase-items.php',
    'functions/fix-user95404-password.php',
    'functions/payment-success-issue-solution.php',
    'functions/production-deployment-analysis.php',
    'functions/signin-fix-summary.php',
    'functions/webhook-issue-solution.php',
    'functions/check-database-data.php',
    'functions/check-guest-password.php',
    'functions/check-recent-purchase.php',
    
    // Log files
    'front-end/webhook.log',
    'functions/password_reset.log',
    'front-end/contact-submissions.log',
    
    // Backup files
    'front-end/user-menu copy.php',
    
    // Other test files
    'front-end/latest-purchase.php',
    'front-end/check-recent-users.php',
];

// Files to keep (important for functionality)
$important_files = [
    'front-end/email-config.php', // Email configuration
    'front-end/stripe-webhook.php', // Production webhook
    'front-end/payment-success.php', // Payment processing
    'functions/server.php', // Database connection
    'functions/buyprocess.php', // Purchase processing
    'functions/graphql_functions.php', // API functions
];

echo "<h2>📋 Files to Remove (" . count($test_files_to_remove) . " files)</h2>";

$removed_count = 0;
$not_found_count = 0;
$error_count = 0;

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🗑️ Removal Progress:</h3>";

foreach ($test_files_to_remove as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            echo "<p style='color: green;'>✅ Removed: $file</p>";
            $removed_count++;
        } else {
            echo "<p style='color: red;'>❌ Failed to remove: $file</p>";
            $error_count++;
        }
    } else {
        echo "<p style='color: gray;'>⚪ Not found: $file</p>";
        $not_found_count++;
    }
}

echo "</div>";

echo "<hr>";
echo "<h2>📊 Cleanup Summary</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Cleanup Results:</h3>";
echo "<ul>";
echo "<li><strong>Files Removed:</strong> $removed_count</li>";
echo "<li><strong>Files Not Found:</strong> $not_found_count</li>";
echo "<li><strong>Removal Errors:</strong> $error_count</li>";
echo "<li><strong>Total Processed:</strong> " . count($test_files_to_remove) . "</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>🔒 Important Files Kept</h2>";

echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ These important files were preserved:</h3>";
echo "<ul>";
foreach ($important_files as $file) {
    $status = file_exists($file) ? '✅ Present' : '❌ Missing';
    echo "<li><strong>$file:</strong> $status</li>";
}
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>💾 Storage Savings</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📈 Estimated Storage Saved:</h3>";
echo "<ul>";
echo "<li><strong>Test Files Removed:</strong> $removed_count files</li>";
echo "<li><strong>Estimated Size Saved:</strong> " . ($removed_count * 15) . " KB - " . ($removed_count * 50) . " KB</li>";
echo "<li><strong>Log Files Cleaned:</strong> webhook.log, password_reset.log, contact-submissions.log</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>🎯 What Was Cleaned</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🧹 Categories of Files Removed:</h3>";
echo "<ul>";
echo "<li><strong>Payment Testing:</strong> test-payment-*, debug-payment-*, test-stripe-*</li>";
echo "<li><strong>Cart Testing:</strong> test-cart-*, test-guest-cart*</li>";
echo "<li><strong>Authentication Testing:</strong> test-signin-*, test-password-*, debug-signin-*</li>";
echo "<li><strong>Webhook Testing:</strong> test-webhook-*, simulate-webhook.php</li>";
echo "<li><strong>Address Testing:</strong> test-address2-*, test-suburb-*</li>";
echo "<li><strong>UI Testing:</strong> test-modal-*, test-credentials-*, test-clipboard-*</li>";
echo "<li><strong>Deployment Testing:</strong> deployment-checklist.php, cpanel-deployment-*</li>";
echo "<li><strong>Debug Files:</strong> debug-*, quick-debug.php, phpinfo.php</li>";
echo "<li><strong>Log Files:</strong> *.log files</li>";
echo "<li><strong>Backup Files:</strong> *copy.php files</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>✅ Production Ready</h2>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #0dcaf0;'>";
echo "<h3>🚀 Your Project is Now Clean!</h3>";
echo "<p><strong>All test and debug files have been removed.</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Storage optimized:</strong> Removed unnecessary test files</li>";
echo "<li>✅ <strong>Production ready:</strong> Only essential files remain</li>";
echo "<li>✅ <strong>Clean codebase:</strong> No development clutter</li>";
echo "<li>✅ <strong>Secure:</strong> No debug information exposed</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>🔄 Next Steps</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 After Cleanup:</h3>";
echo "<ol>";
echo "<li><strong>Test Core Functions:</strong> Verify payment, cart, and user functions still work</li>";
echo "<li><strong>Remove This Cleanup File:</strong> Delete cleanup-test-files.php after use</li>";
echo "<li><strong>Upload to Server:</strong> Your project is now ready for production deployment</li>";
echo "<li><strong>Final Testing:</strong> Test all critical features on the server</li>";
echo "</ol>";
echo "</div>";

if ($removed_count > 0) {
    echo "<div style='text-align: center; background: #28a745; color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>🎉 CLEANUP SUCCESSFUL!</h2>";
    echo "<p><strong>Removed $removed_count test files and optimized your project for production!</strong></p>";
    echo "</div>";
}
?>
