<?php
include('functions/server.php');

// Create payment_methods table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS payment_methods (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    payment_method_id VARCHAR(255) NOT NULL,
    card_last4 VARCHAR(4) NOT NULL,
    card_brand VARCHAR(50) NOT NULL,
    card_exp_month INT(2) NOT NULL,
    card_exp_year INT(4) NOT NULL,
    is_default TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY (user_id, payment_method_id),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);";

if ($conn->query($sql) === TRUE) {
    echo "Payment methods table created successfully";
} else {
    echo "Error creating table: " . $conn->error;
}

$conn->close();
?>
