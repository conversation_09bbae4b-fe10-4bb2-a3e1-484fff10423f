<?php
/**
 * Customer Timezone Functions Demo
 * Shows how to use customer-specific timezone functions
 */

// Include the database connection and timezone helper
include('functions/server.php');

echo "<h1>👤 Customer Timezone Functions Demo</h1>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>✅ Customer-Specific Timezone Functions</h2>";
echo "<p><strong>Purpose:</strong> Show time in customer's timezone automatically</p>";
echo "<p><strong>Target:</strong> Front-end customer pages only</p>";
echo "<p><strong>Features:</strong> Auto-detection, relative time, user-friendly formats</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🔧 Available Customer Functions</h2>";

// Test all customer timezone functions
$test_utc_time = getCurrentUTC();

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Function Examples:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Function</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Result</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Use Case</th>";
echo "</tr>";

// Test getCustomerTimezone()
$customer_timezone = getCustomerTimezone();
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>getCustomerTimezone()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_timezone</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Get customer's timezone</td>";
echo "</tr>";

// Test showCustomerTime()
$customer_time = showCustomerTime($test_utc_time);
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTime()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_time</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>User-friendly format</td>";
echo "</tr>";

// Test showCustomerTimeSimple()
$customer_time_simple = showCustomerTimeSimple($test_utc_time);
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeSimple()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_time_simple</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Simple Y-m-d H:i format</td>";
echo "</tr>";

// Test showCustomerTimeRelative()
$past_time = date('Y-m-d H:i:s', strtotime($test_utc_time . ' -2 hours'));
$customer_relative = showCustomerTimeRelative($past_time);
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeRelative()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_relative</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Relative time (e.g., '2 hours ago')</td>";
echo "</tr>";

// Test getCustomerCurrentTime()
$customer_current = getCustomerCurrentTime();
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>getCustomerCurrentTime()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_current</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Current time in customer's timezone</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>📝 Usage Examples in Customer Pages</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎯 Example 1: Ticket Log Display</h3>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// In front-end/my-ticket-log.php
while ($log = mysqli_fetch_assoc($resultLogs)) {
    echo "<tr>";
    echo "<td>" . showCustomerTime($log["created_at"]) . "</td>";
    echo "<td>" . $log["action"] . "</td>";
    echo "<td>" . $log["description"] . "</td>";
    echo "</tr>";
}

// Result: Shows "Dec 20, 2024 4:17 PM" instead of UTC time
');
echo "</pre>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎯 Example 2: Support Ticket Display</h3>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// In front-end/my-tickets.php
while ($ticket = mysqli_fetch_assoc($result)) {
    echo "<div class=\"ticket-card\">";
    echo "<h3>" . $ticket["title"] . "</h3>";
    echo "<p>Created: " . showCustomerTime($ticket["created_at"]) . "</p>";
    echo "<p>Last updated: " . showCustomerTimeRelative($ticket["updated_at"]) . "</p>";
    echo "</div>";
}

// Result: 
// Created: Dec 20, 2024 4:17 PM
// Last updated: 2 hours ago
');
echo "</pre>";
echo "</div>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎯 Example 3: Profile Page</h3>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// In front-end/profile.php
echo "<p>Account created: " . showCustomerTime($user["created_at"]) . "</p>";
echo "<p>Last login: " . showCustomerTimeRelative($user["last_login"]) . "</p>";
echo "<p>Current time: " . getCustomerCurrentTime("g:i A") . "</p>";

// Result:
// Account created: Nov 15, 2024 2:30 PM
// Last login: 3 days ago  
// Current time: 4:17 PM
');
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h2>🌐 JavaScript Integration</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📱 Automatic Timezone Detection:</h3>";
echo "<p>Include the JavaScript file in your customer pages:</p>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo htmlspecialchars('
<!-- Add to customer pages -->
<script src="js/customer-timezone.js"></script>

<!-- Optional: Manual timezone elements -->
<div class="customer-time" data-utc-time="2024-12-20 08:17:00">
    <!-- Will be automatically converted to customer time -->
</div>

<div class="customer-time-relative" data-utc-time="2024-12-20 06:17:00">
    <!-- Will show "2 hours ago" -->
</div>
');
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h2>🧪 Live Customer Time Test</h2>";

// Create some test data
$test_times = [
    'now' => getCurrentUTC(),
    '1_hour_ago' => date('Y-m-d H:i:s', strtotime(getCurrentUTC() . ' -1 hour')),
    '1_day_ago' => date('Y-m-d H:i:s', strtotime(getCurrentUTC() . ' -1 day')),
    '1_week_ago' => date('Y-m-d H:i:s', strtotime(getCurrentUTC() . ' -1 week'))
];

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📊 Customer Time Display Test:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Scenario</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>UTC (Database)</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Customer Display</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Relative Time</th>";
echo "</tr>";

foreach ($test_times as $label => $utc_time) {
    $display_time = showCustomerTime($utc_time);
    $relative_time = showCustomerTimeRelative($utc_time);
    
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>" . ucfirst(str_replace('_', ' ', $label)) . "</strong></td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$utc_time</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$display_time</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$relative_time</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>⚙️ Customer Timezone Settings</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🔧 Timezone Detection Priority:</h3>";
echo "<ol>";
echo "<li><strong>Customer Profile:</strong> Saved timezone in database</li>";
echo "<li><strong>Session:</strong> Detected timezone in current session</li>";
echo "<li><strong>JavaScript Detection:</strong> Browser-detected timezone</li>";
echo "<li><strong>Default:</strong> Asia/Singapore</li>";
echo "</ol>";

echo "<h4>💾 How to Save Customer Timezone:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// Save customer timezone preference
if (isset($_POST["customer_timezone"])) {
    $timezone = $_POST["customer_timezone"];
    if (saveCustomerTimezone($timezone)) {
        echo "Timezone saved successfully!";
    }
}

// JavaScript will automatically detect and save timezone
// No manual intervention needed for most customers
');
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h2>📋 Implementation Checklist</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Ready to Use:</h3>";
echo "<ol>";
echo "<li>✅ <strong>Customer timezone functions created</strong></li>";
echo "<li>✅ <strong>JavaScript auto-detection ready</strong></li>";
echo "<li>✅ <strong>Database timezone column support</strong></li>";
echo "<li>✅ <strong>Session management included</strong></li>";
echo "<li>🔧 <strong>Update customer pages:</strong> Replace time displays</li>";
echo "<li>🔧 <strong>Include JavaScript:</strong> Add to customer pages</li>";
echo "<li>🔧 <strong>Test with customers:</strong> Verify timezone display</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; background: #28a745; color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🎉 CUSTOMER TIMEZONE FUNCTIONS READY!</h2>";
echo "<p><strong>Use these functions in your customer pages for automatic timezone display!</strong></p>";
echo "<p>Customers will see times in their local timezone automatically.</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🚀 Quick Start Guide</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Implementation Steps:</h3>";
echo "<ol>";
echo "<li><strong>Include JavaScript:</strong> Add <code>&lt;script src=\"js/customer-timezone.js\"&gt;&lt;/script&gt;</code> to customer pages</li>";
echo "<li><strong>Replace time displays:</strong> Use <code>showCustomerTime()</code> instead of direct database time</li>";
echo "<li><strong>Add relative times:</strong> Use <code>showCustomerTimeRelative()</code> for \"X hours ago\" display</li>";
echo "<li><strong>Test with different timezones:</strong> Verify display is correct</li>";
echo "</ol>";

echo "<h4>🎯 Example Update:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// BEFORE (shows UTC to customer)
echo "Created: " . $row["created_at"];

// AFTER (shows customer\'s local time)
echo "Created: " . showCustomerTime($row["created_at"]);
');
echo "</pre>";
echo "</div>";

// Include the JavaScript for demonstration
echo "<script src='js/customer-timezone.js'></script>";
echo "<script>";
echo "// Demo: Show customer timezone info";
echo "setTimeout(() => {";
echo "    const info = window.CustomerTimezone.getCustomerTimezoneInfo();";
echo "    console.log('Customer Timezone Info:', info);";
echo "}, 2000);";
echo "</script>";
?>