RewriteEngine On

# Remove .php extension from URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect .php extension to clean URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Handle specific pages for clean URLs
RewriteRule ^privacy-policy/?$ privacy-policy.php [NC,L]
RewriteRule ^terms-conditions/?$ terms-conditions.php [NC,L]
RewriteRule ^contact/?$ contact.php [NC,L]
RewriteRule ^pricing/?$ pricing.php [NC,L]
RewriteRule ^about/?$ about.php [NC,L]
RewriteRule ^faq/?$ faq.php [NC,L]
RewriteRule ^issues-we-solve/?$ issues-we-solve.php [NC,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to PHP configuration files
<Files "*.ini">
    Order allow,deny
    Deny from all
</Files>
