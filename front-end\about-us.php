<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>About Us</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
        include('../header-footer/newnavtest.php');
        ?>
        <!-- navbar-dark -->
        <!-- navbar- -->
        <!-- Section 1 -->
        <style>
        /* Base styles */
        .inner-banner {
            background-color: #f9fafd;
            padding-top: 80px;
            padding-bottom: 60px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding-left: 20px;
            padding-right: 20px;
        }

        .row {
            display: flex;
            flex-wrap: wrap;
            margin-left: -15px;
            margin-right: -15px;
            justify-content: center;
        }

        /* Company Introduction Section */
        .about-content {
            background: linear-gradient(to right, #6a11cb, #2575fc);
            padding: 80px 0;
        }

        .about-content h2 {
            color: #fff;
            font-size: 2.5rem;
            margin-bottom: 30px;
        }

        .about-content p {
            color: #fff;
            font-size: 19px;
            line-height: 32px;
            margin-bottom: 20px;
        }

        /* Feature Section */
        .feature-section {
            padding: 70px 0 35px;
        }

        .feature-widget {
            text-align: center;
            margin-bottom: 40px;
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-widget:hover {
            transform: translateY(-5px);
        }

        .widget-icon {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .widget-text h3 {
            font-size: 21px;
            margin-bottom: 15px;
        }

        .widget-text p {
            font-size: 16px;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section-1 {
            background-color: #005B91;
            padding: 70px 0;
        }

        .cta-text h2 {
            color: white;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .cta-text p {
            color: white;
            font-size: 1rem;
            line-height: 1.6;
        }

        .cta-btn {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-end;
        }

        .cta-btn a {
            margin: 5px;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .cta-btn a:hover {
            transform: translateY(-3px);
        }

        /* Responsive styles */
        /* For desktops and large screens */
        @media (min-width: 1200px) {
            .about-content h2 {
                margin-left: 150px;
            }

            .pt-lg-34,
            .py-lg-34 {
                /* padding-top: 14.0625rem !important; */
                margin-top: -240px !important;
            }
        }

        /* For medium desktops */
        @media (min-width: 992px) and (max-width: 1199px) {
            .about-content h2 {
                margin-left: 100px;
                font-size: 2.2rem;
            }

            .pt-lg-34,
            .py-lg-34 {
                /* padding-top: 14.0625rem !important; */
                margin-top: -240px !important;
            }

            .about-content p {
                font-size: 17px;
                line-height: 30px;
            }

            .widget-text h3 {
                font-size: 19px;
            }

            .widget-text p {
                font-size: 15px;
            }

            .cta-text h2 {
                font-size: 1.8rem;
            }
        }

        /* For tablets */
        @media (min-width: 768px) and (max-width: 991px) {
            .inner-banner {
                padding-top: 60px;
                padding-bottom: 40px;
            }

            .title.gr-text-2 {
                font-size: 2rem;
                line-height: 1.3;
            }

            .gr-text-8 {
                font-size: 16px;
                line-height: 28px;
            }

            .about-content {
                padding: 60px 0;
            }

            .about-content h2 {
                margin-left: 50px;
                font-size: 2rem;
                margin-bottom: 20px;
            }

            .about-content p {
                font-size: 16px;
                line-height: 28px;
            }

            .feature-section {
                padding: 50px 0 20px;
            }

            .widget-icon {
                width: 70px;
                height: 70px;
                margin-bottom: 20px;
            }

            .widget-text h3 {
                font-size: 18px;
            }

            .widget-text p {
                font-size: 14px;
            }

            .cta-section-1 {
                padding: 50px 0;
            }

            .cta-text h2 {
                font-size: 1.6rem;
            }

            .cta-text p {
                font-size: 15px;
            }

            .cta-btn {
                margin-top: 20px;
                justify-content: center;
            }
        }

        /* For mobile phones */
        @media (max-width: 767px) {
            .inner-banner {
                padding-top: 40px;
                padding-bottom: 30px;
            }

            .title.gr-text-2 {
                font-size: 1.7rem;
                line-height: 1.3;
            }

            .gr-text-8 {
                font-size: 15px;
                line-height: 26px;
            }

            .about-content {
                padding: 40px 0;
                text-align: center;
            }

            .about-content h2 {
                margin-left: 0;
                font-size: 1.7rem;
                margin-bottom: 20px;
            }

            .about-content p {
                font-size: 15px;
                line-height: 26px;
                text-align: center;
            }

            .feature-section {
                padding: 40px 0 10px;
            }

            .feature-widget {
                margin-bottom: 30px;
            }

            .widget-icon {
                width: 60px;
                height: 60px;
                margin-bottom: 15px;
            }

            .widget-text h3 {
                font-size: 17px;
                margin-bottom: 10px;
            }

            .widget-text p {
                font-size: 14px;
            }

            .cta-section-1 {
                padding: 40px 0;
                text-align: center;
            }

            .cta-text h2 {
                font-size: 1.5rem;
                margin-bottom: 15px;
            }

            .cta-text p {
                font-size: 14px;
                margin-bottom: 20px;
            }

            .cta-btn {
                justify-content: center;
                flex-direction: column;
            }

            .cta-btn a {
                margin: 5px 0;
                width: 100%;
                text-align: center;
            }
        }

        /* For small mobile phones */
        @media (max-width: 575px) {
            .title.gr-text-2 {
                font-size: 1.5rem;
            }

            .gr-text-8 {
                font-size: 14px;
                line-height: 24px;
            }

            .about-content h2 {
                font-size: 1.5rem;
            }

            .about-content p {
                font-size: 14px;
                line-height: 24px;
            }

            .widget-text h3 {
                font-size: 16px;
            }

            .widget-text p {
                font-size: 13px;
            }

            .cta-text h2 {
                font-size: 1.4rem;
            }
        }
        </style>
        <div class="inner-banner" data-aos="fade-up" data-aos-duration="1000" data-aos-once="true">
            <div class="container">
                <div class="row  justify-content-center mt-md-6 pt-29 pt-lg-34">
                    <div class="col-xl-8 col-lg-9">
                        <div class="px-md-12 text-center mb-11 mb-lg-14">
                            <h2 class="title gr-text-2 mb-9 mb-lg-12"
                                style="font-family: 'Circular Std', sans-serif; font-weight: 700; font-size: 60px; line-height: 65px;">
                                How HelloIT Started</h2>
                            <p class="gr-text-8 mb-0"
                                style="font-family: 'Circular Std', sans-serif; font-weight: 400; font-size: 19px; line-height: 32px;">
                                Our story is about how passion turns dreams into a reality. We love IT and are thrilled
                                fixing them. Our passion in solving IT issues has led to the creation of HelloIT.</p>
                        </div>
                    </div>
                    <div class=" col-12">
                        <div class="banner-fluid-image pt-lg-9" data-aos="zoom-in" data-aos-duration="1000">
                            <img src="../image/wp/about-us.svg" alt="" class="w-100">
                        </div>
                    </div>
                </div>
            </div>
        </div>






        <div class="about-content pt-lg-28 pt-13 pb-13 pb-lg-25"
            style="background: linear-gradient(to right, #6a11cb, #2575fc);">
            <div class="container">
                <div class="row" data-aos="fade-left" data-aos-duration="1100" data-aos-once="true">
                    <div class="col-lg-6 mb-7 mb-lg-0">
                        <div class="pr-xl-13">
                            <h2 class="gr-text-3 mb-0 company-intro-heading">
                                Company <br>Introduction</h2>
                        </div>
                    </div>
                    <style>
                    .company-intro-heading {
                        margin-left: 150px;
                        color: #fff;
                    }

                    @media (max-width: 767px) {
                        .company-intro-heading {
                            margin-left: 0 !important;
                            text-align: center !important;
                        }

                        .col-lg-6 {
                            text-align: center;
                        }
                    }
                    </style>
                    <div class="col-lg-6">
                        <div class="pr-xl-13">
                            <p class="gr-text-8 mb-7 mb-lg-10"
                                style="font-family: 'Circular Std', sans-serif; font-weight: 400; font-size: 19px; line-height: 32px; color: #fff;">
                                HelloIT is a global technology company that helps enterprises re-imagine their
                                businesses for the digital age.</p>
                            <p class="gr-text-8 mb-7 mb-lg-10"
                                style="font-family: 'Circular Std', sans-serif; font-weight: 400; font-size: 19px; line-height: 32px; color: #fff;">
                                Our technology products and services are built on decades of innovation, proven work
                                processes and knowledge to bring the best experience to our customer.</p>
                            <p class="gr-text-8 mb-0"
                                style="font-family: 'Circular Std', sans-serif; font-weight: 400; font-size: 19px; line-height: 32px; color: #fff;">
                                HelloIT also takes pride in its many diversity, social responsibility, sustainability,
                                and education initiatives.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Feature section -->
        <div class="feature-section pt-14 pt-lg-21 pb-7 bg-default-6">
            <div class="container">
                <div class="row align-items-center justify-content-center">
                    <div class="col-lg-4 col-md-6 mb-11 mb-lg-19 px-xs-6 px-md-6 px-lg-0 px-xl-8" data-aos="fade-right"
                        data-aos-duration="800" data-aos-once="true">
                        <div class="feature-widget text-center">
                            <div class="widget-icon square-80 rounded-15 mx-auto mb-9 mb-lg-12 bg-blue shadow-blue">
                                <img src="../image/svg/feature8-icon1.svg" alt="">
                            </div>
                            <div class="widget-text">
                                <h3 class="title gr-text-6 mb-7">Who We Are</h3>
                                <p class="gr-text-11 mb-0">HelloIT is an online IT support desk committed to deliver the
                                    best IT support experience to our customer. Our global IT service desk services are
                                    comprised of certified, IT professionals who we trained to resolve IT issues in a
                                    timely manner so that our clients can focus in growing their businesses.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-11 mb-lg-19 px-xs-6 px-md-6 px-lg-0 px-xl-8" data-aos="fade-right"
                        data-aos-duration="800" data-aos-delay="400" data-aos-once="true">
                        <div class="feature-widget text-center">
                            <div class="widget-icon square-80 rounded-15 mx-auto mb-9 mb-lg-12 bg-red shadow-red">
                                <img src="../image/svg/feature8-icon2.svg" alt="">
                            </div>
                            <div class="widget-text">
                                <h3 class="title gr-text-6 mb-7">What We Do</h3>
                                <p class="gr-text-11 mb-0">We work with our clients to offer high-quality business IT
                                    support desk and remote access services. Much more than a traditional support desk,
                                    HelloIT service desk services provide our customers with professional and immediate
                                    help desk support without the costly overhead.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-11 mb-lg-19 px-xs-6 px-md-6 px-lg-0 px-xl-8" data-aos="fade-right"
                        data-aos-duration="800" data-aos-delay="600" data-aos-once="true">
                        <div class="feature-widget text-center">
                            <div class="widget-icon square-80 rounded-15 mx-auto mb-9 mb-lg-12 bg-green shadow-green">
                                <img src="../image/svg/feature8-icon3.svg" alt="">
                            </div>
                            <div class="widget-text">
                                <h3 class="title gr-text-6 mb-7">How We Do it</h3>
                                <p class="gr-text-11 mb-0">HelloIT utilizes proprietary IT Service Management platform
                                    (ITSM) which enables our IT service desk support staff to resolve support issues
                                    quickly and efficiently as they occur. Our unique support workflow and IT knowledge
                                    enable us to focus on rapid triage and in solving the problem right the first time.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- CTA section -->
        <div class="cta-section-1 py-12 pt-lg-20 pb-lg-18 " style="background-color: #005B91;">
            <div class="container">
                <div class="row align-items-center" data-aos="fade-up" data-aos-duration="1000" data-aos-once="true">
                    <div class="col-lg-6">
                        <div class="section-title cta-text pr-lg-5">
                            <h2 class="gr-text-5 mb-7" style="color: white;">Stop IT Issues From Hindering Your Growth!​
                            </h2>
                            <p class="gr-text-8" style="color: white;">Join hundreds of other businesses that uses
                                HelloIT technical support services to solve their IT issues and make their business more
                                efficient than before.</p>
                        </div>
                    </div>
                    <div class="col-lg-6 offset-xl-1 col-xl-5">
                        <div class="cta-btn d-flex flex-column flex-sm-row justify-content-lg-end mt-5 mt-lg-0">
                            <a href="#"
                                class="btn gr-bg-blue-opacity-1 text-primary--light-only gr-hover-y with-icon mr-sm-7 mb-6 mb-sm-0"
                                style="background-color: orange; color: white;">Get Started</a>
                            <a href="#" class="btn btn-primary with-icon gr-hover-y"
                                style="background-color: #039BC4; color: white;">Learn More</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer section -->
        <?php
        include('../header-footer/footer.php');
        ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>