<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Privacy Policy - HelloIT</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
    <style>
    /* Custom responsive styles for privacy policy page */
    body {
        overflow-x: hidden;
    }

    .inner-banner-simple {
        margin-top: -100px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;
        overflow: hidden;
        min-height: 400px;
        display: flex;
        align-items: center;
    }

    .inner-banner-simple::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
    }

    .page-main-title {
        font-size: 3.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
    }

    .page-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        line-height: 1.6;
        animation: fadeInUp 1s ease-out 0.2s both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .content-section {
        background: #f8f9fa;
        padding: 80px 0;
    }

    .content-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .content-title {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 2rem;
        position: relative;
    }

    .content-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .section-heading {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }

    .content-text {
        color: #666;
        line-height: 1.8;
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .content-list {
        color: #666;
        line-height: 1.8;
        margin-bottom: 1.5rem;
        padding-left: 1.5rem;
    }

    .content-list li {
        margin-bottom: 0.5rem;
    }

    .highlight-box {
        background: #f0f4ff;
        border-left: 4px solid #667eea;
        padding: 1.5rem;
        margin: 2rem 0;
        border-radius: 8px;
    }

    .last-updated {
        font-style: italic;
        color: #888;
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #eee;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .inner-banner-simple {
            min-height: 300px;
            padding: 100px 0 50px;
        }

        .page-main-title {
            font-size: 2.5rem;
        }

        .page-subtitle {
            font-size: 1rem;
        }

        .content-section {
            padding: 50px 0;
        }

        .content-card {
            padding: 2rem 1.5rem;
        }

        .content-title {
            font-size: 1.8rem;
        }

        .section-heading {
            font-size: 1.3rem;
        }

        .content-text {
            font-size: 0.95rem;
        }
    }

    @media (max-width: 480px) {
        .inner-banner-simple {
            min-height: 250px;
            padding: 80px 0 40px;
        }

        .page-main-title {
            font-size: 2rem;
        }

        .page-subtitle {
            font-size: 0.9rem;
        }

        .content-card {
            padding: 1.5rem 1rem;
        }

        .content-title {
            font-size: 1.5rem;
        }

        .section-heading {
            font-size: 1.2rem;
        }

        .content-text {
            font-size: 0.9rem;
        }
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
        include('../header-footer/newnavtest.php');
        ?>
        
        <!-- Hero Banner -->
        <div class="inner-banner-simple pt-29 pb-50">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-hero-content">
                            <h1 class="page-main-title">Privacy Policy</h1>
                            <p class="page-subtitle">Your privacy is important to us. Learn how we collect, use, and protect your information.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="content-card">
                            <h2 class="content-title">Privacy Policy</h2>
                            
                            <div class="highlight-box">
                                <p class="content-text mb-0"><strong>Effective Date:</strong> January 1, 2024</p>
                            </div>

                            <p class="content-text">
                                At HelloIT, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or use our services.
                            </p>

                            <h3 class="section-heading">1. Information We Collect</h3>
                            <p class="content-text">We may collect information about you in a variety of ways:</p>
                            <ul class="content-list">
                                <li><strong>Personal Data:</strong> Name, email address, phone number, postal address, and other contact information</li>
                                <li><strong>Technical Data:</strong> IP address, browser type, operating system, and device information</li>
                                <li><strong>Usage Data:</strong> Information about how you use our website and services</li>
                                <li><strong>Communication Data:</strong> Records of your communications with us</li>
                            </ul>

                            <h3 class="section-heading">2. How We Use Your Information</h3>
                            <p class="content-text">We use the information we collect to:</p>
                            <ul class="content-list">
                                <li>Provide, operate, and maintain our services</li>
                                <li>Process transactions and send related information</li>
                                <li>Send administrative information and updates</li>
                                <li>Respond to your comments, questions, and requests</li>
                                <li>Improve our website and services</li>
                                <li>Monitor and analyze usage patterns and trends</li>
                            </ul>

                            <h3 class="section-heading">3. Information Sharing and Disclosure</h3>
                            <p class="content-text">
                                We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share your information in the following circumstances:
                            </p>
                            <ul class="content-list">
                                <li>With service providers who assist us in operating our website and services</li>
                                <li>To comply with legal obligations or respond to lawful requests</li>
                                <li>To protect our rights, property, or safety, or that of others</li>
                                <li>In connection with a business transfer or merger</li>
                            </ul>

                            <h3 class="section-heading">4. Data Security</h3>
                            <p class="content-text">
                                We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.
                            </p>

                            <h3 class="section-heading">5. Your Rights</h3>
                            <p class="content-text">Depending on your location, you may have the following rights:</p>
                            <ul class="content-list">
                                <li>Access to your personal information</li>
                                <li>Correction of inaccurate information</li>
                                <li>Deletion of your personal information</li>
                                <li>Restriction of processing</li>
                                <li>Data portability</li>
                                <li>Objection to processing</li>
                            </ul>

                            <h3 class="section-heading">6. Cookies and Tracking Technologies</h3>
                            <p class="content-text">
                                We use cookies and similar tracking technologies to enhance your experience on our website. You can control cookie settings through your browser preferences.
                            </p>

                            <h3 class="section-heading">7. Third-Party Links</h3>
                            <p class="content-text">
                                Our website may contain links to third-party websites. We are not responsible for the privacy practices or content of these external sites.
                            </p>

                            <h3 class="section-heading">8. Changes to This Policy</h3>
                            <p class="content-text">
                                We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the effective date.
                            </p>

                            <h3 class="section-heading">9. Contact Us</h3>
                            <p class="content-text">
                                If you have any questions about this Privacy Policy, please contact us at:
                            </p>
                            <ul class="content-list">
                                <li><strong>Email:</strong> <EMAIL></li>
                                <li><strong>Address:</strong> 170 Upper Bt. Timah Rd. #02-10, Singapore 588179</li>
                            </ul>

                            <div class="last-updated">
                                <p>Last updated: January 1, 2024</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer section -->
        <?php
        include('../header-footer/footer.php');
        ?>
    </div>
    
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>
