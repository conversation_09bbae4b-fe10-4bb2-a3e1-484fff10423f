<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Terms & Conditions - HelloIT</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
    <style>
    /* Custom responsive styles for terms & conditions page */
    body {
        overflow-x: hidden;
    }

    .inner-banner-simple {
        margin-top: -100px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;
        overflow: hidden;
        min-height: 400px;
        display: flex;
        align-items: center;
    }

    .inner-banner-simple::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
    }

    .page-main-title {
        font-size: 3.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
    }

    .page-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        line-height: 1.6;
        animation: fadeInUp 1s ease-out 0.2s both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .content-section {
        background: #f8f9fa;
        padding: 80px 0;
    }

    .content-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .content-title {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 2rem;
        position: relative;
    }

    .content-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .section-heading {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }

    .content-text {
        color: #666;
        line-height: 1.8;
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .content-list {
        color: #666;
        line-height: 1.8;
        margin-bottom: 1.5rem;
        padding-left: 1.5rem;
    }

    .content-list li {
        margin-bottom: 0.5rem;
    }

    .highlight-box {
        background: #f0f4ff;
        border-left: 4px solid #667eea;
        padding: 1.5rem;
        margin: 2rem 0;
        border-radius: 8px;
    }

    .last-updated {
        font-style: italic;
        color: #888;
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #eee;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .inner-banner-simple {
            min-height: 300px;
            padding: 100px 0 50px;
        }

        .page-main-title {
            font-size: 2.5rem;
        }

        .page-subtitle {
            font-size: 1rem;
        }

        .content-section {
            padding: 50px 0;
        }

        .content-card {
            padding: 2rem 1.5rem;
        }

        .content-title {
            font-size: 1.8rem;
        }

        .section-heading {
            font-size: 1.3rem;
        }

        .content-text {
            font-size: 0.95rem;
        }
    }

    @media (max-width: 480px) {
        .inner-banner-simple {
            min-height: 250px;
            padding: 80px 0 40px;
        }

        .page-main-title {
            font-size: 2rem;
        }

        .page-subtitle {
            font-size: 0.9rem;
        }

        .content-card {
            padding: 1.5rem 1rem;
        }

        .content-title {
            font-size: 1.5rem;
        }

        .section-heading {
            font-size: 1.2rem;
        }

        .content-text {
            font-size: 0.9rem;
        }
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
        include('../header-footer/newnavtest.php');
        ?>

        <!-- Hero Banner -->
        <div class="inner-banner-simple pt-29 pb-50">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-hero-content">
                            <h1 class="page-main-title">Terms & Conditions</h1>
                            <p class="page-subtitle">Please read these terms carefully before using our services.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="content-card">
                            <h2 class="content-title">Terms & Conditions</h2>

                            <div class="highlight-box">
                                <p class="content-text mb-0"><strong>Effective Date:</strong> January 1, 2024</p>
                            </div>

                            <p class="content-text">
                                Welcome to HelloIT. These Terms and Conditions ("Terms") govern your use of our website and services. By accessing or using our services, you agree to be bound by these Terms.
                            </p>

                            <h3 class="section-heading">1. Acceptance of Terms</h3>
                            <p class="content-text">
                                By accessing and using HelloIT's services, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
                            </p>

                            <h3 class="section-heading">2. Description of Service</h3>
                            <p class="content-text">
                                HelloIT provides technical support services including but not limited to:
                            </p>
                            <ul class="content-list">
                                <li>Remote technical assistance</li>
                                <li>Troubleshooting and problem resolution</li>
                                <li>Software installation and configuration</li>
                                <li>System optimization and maintenance</li>
                                <li>Email and chat support</li>
                            </ul>

                            <h3 class="section-heading">3. User Accounts</h3>
                            <p class="content-text">
                                To access certain features of our service, you may be required to create an account. You are responsible for:
                            </p>
                            <ul class="content-list">
                                <li>Maintaining the confidentiality of your account credentials</li>
                                <li>All activities that occur under your account</li>
                                <li>Providing accurate and complete information</li>
                                <li>Notifying us immediately of any unauthorized use</li>
                            </ul>

                            <h3 class="section-heading">4. Payment Terms</h3>
                            <p class="content-text">
                                Payment for services is processed through secure third-party payment processors. By purchasing our services, you agree to:
                            </p>
                            <ul class="content-list">
                                <li>Pay all fees associated with your selected service plan</li>
                                <li>Provide accurate billing information</li>
                                <li>Accept responsibility for all charges incurred</li>
                                <li>Comply with the payment processor's terms of service</li>
                            </ul>

                            <h3 class="section-heading">5. Refund Policy</h3>
                            <p class="content-text">
                                We offer refunds under the following conditions:
                            </p>
                            <ul class="content-list">
                                <li>Service cancellation within 30 days of purchase</li>
                                <li>Inability to provide promised services due to technical limitations</li>
                                <li>Refund requests must be submitted in writing</li>
                                <li>Processing time may take 5-10 business days</li>
                            </ul>

                            <h3 class="section-heading">6. Prohibited Uses</h3>
                            <p class="content-text">You may not use our service:</p>
                            <ul class="content-list">
                                <li>For any unlawful purpose or to solicit others to perform unlawful acts</li>
                                <li>To violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances</li>
                                <li>To infringe upon or violate our intellectual property rights or the intellectual property rights of others</li>
                                <li>To harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate</li>
                                <li>To submit false or misleading information</li>
                            </ul>

                            <h3 class="section-heading">7. Limitation of Liability</h3>
                            <p class="content-text">
                                HelloIT shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the service.
                            </p>

                            <h3 class="section-heading">8. Service Availability</h3>
                            <p class="content-text">
                                We strive to maintain high service availability but cannot guarantee uninterrupted access. We reserve the right to modify, suspend, or discontinue any part of our service with or without notice.
                            </p>

                            <h3 class="section-heading">9. Intellectual Property</h3>
                            <p class="content-text">
                                The service and its original content, features, and functionality are and will remain the exclusive property of HelloIT and its licensors. The service is protected by copyright, trademark, and other laws.
                            </p>

                            <h3 class="section-heading">10. Termination</h3>
                            <p class="content-text">
                                We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever, including without limitation if you breach the Terms.
                            </p>

                            <h3 class="section-heading">11. Changes to Terms</h3>
                            <p class="content-text">
                                We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.
                            </p>

                            <h3 class="section-heading">12. Contact Information</h3>
                            <p class="content-text">
                                If you have any questions about these Terms and Conditions, please contact us at:
                            </p>
                            <ul class="content-list">
                                <li><strong>Email:</strong> <EMAIL></li>
                                <li><strong>Address:</strong> 170 Upper Bt. Timah Rd. #02-10, Singapore 588179</li>
                            </ul>

                            <div class="last-updated">
                                <p>Last updated: January 1, 2024</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer section -->
        <?php
        include('../header-footer/footer.php');
        ?>
    </div>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>