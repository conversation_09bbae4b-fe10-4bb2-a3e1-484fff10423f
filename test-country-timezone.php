<?php
/**
 * Test Country-to-Timezone System
 * Verify that user's country determines their timezone display
 */

// Include the database connection and timezone helper
include('functions/server.php');

echo "<h1>🌍 Test Country-to-Timezone System</h1>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>✅ Country-Based Timezone System</h2>";
echo "<p><strong>Goal:</strong> Display time based on user's country from database</p>";
echo "<p><strong>Source:</strong> user.country column → timezone mapping → time display</p>";
echo "<p><strong>Priority:</strong> 1) Explicit timezone, 2) Country-based, 3) Default</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🗺️ Country-to-Timezone Mapping</h2>";

// Get the country mapping
$country_mapping = UTCTimeHelper::getCountryTimezoneMapping();

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📊 Available Country Mappings (Sample):</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Country</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Timezone</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>UTC Offset</th>";
echo "</tr>";

// Show sample countries
$sample_countries = [
    'Singapore' => 'Asia/Singapore',
    'Thailand' => 'Asia/Bangkok', 
    'United States' => 'America/New_York',
    'United Kingdom' => 'Europe/London',
    'Japan' => 'Asia/Tokyo',
    'Australia' => 'Australia/Sydney',
    'Germany' => 'Europe/Berlin',
    'India' => 'Asia/Kolkata'
];

foreach ($sample_countries as $country => $timezone) {
    try {
        $dt = new DateTime('now', new DateTimeZone($timezone));
        $offset = $dt->format('P');
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>$country</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>$timezone</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>UTC$offset</td>";
        echo "</tr>";
    } catch (Exception $e) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>$country</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>$timezone</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>Error</td>";
        echo "</tr>";
    }
}

echo "</table>";
echo "<p><strong>Total Countries Supported:</strong> " . count($country_mapping) . " countries</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🧪 Test Country-Based Timezone Detection</h2>";

// Test the country-to-timezone function
$test_countries = ['Singapore', 'Thailand', 'United States', 'Germany', 'Japan', 'Unknown Country'];

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🔧 Function Test Results:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Country Input</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Detected Timezone</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
echo "</tr>";

foreach ($test_countries as $country) {
    $detected_timezone = UTCTimeHelper::getTimezoneFromCountry($country);
    $status = $detected_timezone ? '✅ Found' : '❌ Not Found';
    
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$country</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($detected_timezone ?? 'null') . "</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$status</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>👤 Test User Country-Based Display</h2>";

// Get sample users with their countries
try {
    $users_sql = "SELECT id, username, country, timezone FROM user WHERE country IS NOT NULL AND country != '' LIMIT 5";
    $users_result = $conn->query($users_sql);
    
    if ($users_result && $users_result->num_rows > 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📋 Real User Data Test:</h3>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Username</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Country</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Saved Timezone</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Country-Based Timezone</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Final Timezone</th>";
        echo "</tr>";
        
        while ($user = $users_result->fetch_assoc()) {
            $country_timezone = UTCTimeHelper::getTimezoneFromCountry($user['country']);
            $final_timezone = UTCTimeHelper::getUserTimezoneFromProfile($user['id']);
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($user['country']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($user['timezone'] ?? 'None') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($country_timezone ?? 'Not mapped') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>" . ($final_timezone ?? 'Default') . "</strong></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p>No users with country data found for testing.</p>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error retrieving user data:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>⏰ Time Display Simulation</h2>";

// Simulate time display for different countries
$test_utc_time = getCurrentUTC();

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🌍 How Different Countries See the Same UTC Time:</h3>";
echo "<p><strong>UTC Time in Database:</strong> $test_utc_time</p>";

echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>User's Country</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Timezone</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>What They See</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Format</th>";
echo "</tr>";

$demo_countries = [
    'Singapore' => 'Asia/Singapore',
    'Thailand' => 'Asia/Bangkok',
    'United States' => 'America/New_York',
    'United Kingdom' => 'Europe/London',
    'Japan' => 'Asia/Tokyo',
    'Germany' => 'Europe/Berlin'
];

foreach ($demo_countries as $country => $timezone) {
    $user_display = UTCTimeHelper::formatForDisplay($test_utc_time, $timezone, 'Y-m-d H:i');
    $user_friendly = UTCTimeHelper::formatForDisplay($test_utc_time, $timezone, 'M j, Y g:i A');
    
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>$country</strong></td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$timezone</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>$user_display</strong></td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$user_friendly</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>🔧 How It Works in my-ticket-log.php</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Step-by-Step Process:</h3>";
echo "<ol>";
echo "<li><strong>User logs in:</strong> Session contains user_id</li>";
echo "<li><strong>Page loads:</strong> Calls showCustomerTimeSimple(\$log['created_at'])</li>";
echo "<li><strong>Function checks:</strong> getUserTimezoneFromProfile(\$user_id)</li>";
echo "<li><strong>Database query:</strong> SELECT timezone, country FROM user WHERE id = ?</li>";
echo "<li><strong>Priority logic:</strong>";
echo "<ul>";
echo "<li>If timezone column has value → use it</li>";
echo "<li>If country column has value → map to timezone</li>";
echo "<li>Else → use default (Asia/Singapore)</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Display:</strong> Convert UTC time to user's timezone</li>";
echo "</ol>";

echo "<h4>🎯 Example for Thailand User:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
1. User country: "Thailand"
2. Mapped timezone: "Asia/Bangkok" 
3. UTC time: "2024-12-20 08:17:00"
4. Bangkok time: "2024-12-20 15:17"
5. Display: "2024-12-20 15:17"
');
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h2>✅ Implementation Status</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎉 What's Working:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Country mapping:</strong> " . count($country_mapping) . " countries supported</li>";
echo "<li>✅ <strong>Database integration:</strong> Reads user.country column</li>";
echo "<li>✅ <strong>Priority system:</strong> Timezone → Country → Default</li>";
echo "<li>✅ <strong>Auto-save:</strong> Detected timezone saved for future use</li>";
echo "<li>✅ <strong>my-ticket-log.php:</strong> Uses country-based timezone</li>";
echo "</ul>";

echo "<h4>🎯 User Experience:</h4>";
echo "<ul>";
echo "<li><strong>Thailand user:</strong> Sees Bangkok time automatically</li>";
echo "<li><strong>Singapore user:</strong> Sees Singapore time automatically</li>";
echo "<li><strong>US user:</strong> Sees New York time automatically</li>";
echo "<li><strong>UK user:</strong> Sees London time automatically</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>🧪 Test Your Implementation</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Testing Steps:</h3>";
echo "<ol>";
echo "<li><strong>Check user country:</strong> Verify user.country column has values</li>";
echo "<li><strong>Login as user:</strong> Access front-end/my-ticket-log.php</li>";
echo "<li><strong>Check timezone indicator:</strong> Should show country-based timezone</li>";
echo "<li><strong>View ticket logs:</strong> Times should be in user's country timezone</li>";
echo "<li><strong>Create ticket:</strong> New log should show correct local time</li>";
echo "</ol>";

echo "<h4>🔍 Expected Results:</h4>";
echo "<ul>";
echo "<li><strong>Thailand user:</strong> Timezone indicator shows 'Asia/Bangkok'</li>";
echo "<li><strong>Singapore user:</strong> Timezone indicator shows 'Asia/Singapore'</li>";
echo "<li><strong>Times display:</strong> All in user's local timezone</li>";
echo "<li><strong>Database storage:</strong> Still saves in UTC</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; background: #28a745; color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🌍 COUNTRY-BASED TIMEZONE READY!</h2>";
echo "<p><strong>Users now see time in their country's timezone automatically!</strong></p>";
echo "<p>Database saves UTC, display shows user's country time.</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🚀 Next Steps</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Extend to Other Pages:</h3>";
echo "<p>Apply the same country-based timezone to:</p>";
echo "<ul>";
echo "<li><strong>front-end/my-tickets.php:</strong> Support ticket timestamps</li>";
echo "<li><strong>front-end/purchase-history.php:</strong> Purchase dates</li>";
echo "<li><strong>front-end/profile.php:</strong> Account creation date</li>";
echo "<li><strong>merlion/admin pages:</strong> Admin can see user's local time</li>";
echo "</ul>";

echo "<h4>🔧 Add More Countries:</h4>";
echo "<p>Easily add more countries to the mapping in timezone-helper.php</p>";
echo "</div>";
?>
