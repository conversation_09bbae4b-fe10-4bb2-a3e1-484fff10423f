<?php
/**
 * Test Customer Pages Timezone Implementation
 * Verify my-ticket.php and purchase-history.php show correct user timezone
 */

// Include the database connection and timezone helper
include('functions/server.php');

echo "<h1>👤 Test Customer Pages Timezone</h1>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>✅ Customer Pages Updated</h2>";
echo "<p><strong>Updated:</strong> my-ticket.php and purchase-history.php</p>";
echo "<p><strong>Feature:</strong> Show time in user's timezone like my-ticket-log.php</p>";
echo "<p><strong>Added:</strong> Timezone indicators and auto-detection</p>";
echo "</div>";

echo "<hr>";
echo "<h2>📝 Changes Made</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🔧 my-ticket.php Updates:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Change</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Before</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>After</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Time Display</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>\$ticket['created_at']</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeSimple(\$ticket['created_at'])</code></td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Timezone Indicator</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>None</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Shows user's timezone</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>JavaScript</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>No timezone detection</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Added customer-timezone.js</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🔧 purchase-history.php Updates:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Change</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Before</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>After</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Purchase Time</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>\$user['purchase_time']</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeSimple(\$user['purchase_time'])</code></td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Timezone Indicator</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>None</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Shows user's timezone</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>JavaScript</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>No timezone detection</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Added customer-timezone.js</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>🧪 Test Customer Timezone Functions</h2>";

// Test customer timezone functions
$test_utc_time = getCurrentUTC();

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📊 Customer Function Results:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Function</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Result</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Used In</th>";
echo "</tr>";

// Test getCustomerTimezone()
$customer_timezone = getCustomerTimezone();
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>getCustomerTimezone()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_timezone</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Timezone indicators</td>";
echo "</tr>";

// Test showCustomerTimeSimple()
$customer_time_simple = showCustomerTimeSimple($test_utc_time);
echo "<tr style='background: #d4edda;'>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeSimple()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_time_simple</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Both pages - time columns</strong></td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>📊 Sample Data Display</h2>";

// Show sample data for both pages
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎫 Sample Support Ticket (my-ticket.php):</h3>";

// Get sample support ticket
try {
    $sample_ticket_sql = "SELECT id, subject, created_at FROM support_tickets ORDER BY created_at DESC LIMIT 1";
    $sample_ticket_result = $conn->query($sample_ticket_sql);
    
    if ($sample_ticket_result && $sample_ticket_result->num_rows > 0) {
        $ticket = $sample_ticket_result->fetch_assoc();
        $display_time = showCustomerTimeSimple($ticket['created_at']);
        
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Created At</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Ticket ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Subject</th>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>" . $display_time . "</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $ticket['id'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($ticket['subject']) . "</td>";
        echo "</tr>";
        
        echo "</table>";
        echo "<p><strong>UTC in Database:</strong> " . $ticket['created_at'] . "</p>";
        echo "<p><strong>Customer Display:</strong> " . $display_time . "</p>";
    } else {
        echo "<p>No support tickets found for demonstration.</p>";
    }
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>💳 Sample Purchase (purchase-history.php):</h3>";

// Get sample purchase
try {
    $sample_purchase_sql = "SELECT purchaseid, purchase_time, ticket_type, total FROM purchasetickets ORDER BY purchase_time DESC LIMIT 1";
    $sample_purchase_result = $conn->query($sample_purchase_sql);
    
    if ($sample_purchase_result && $sample_purchase_result->num_rows > 0) {
        $purchase = $sample_purchase_result->fetch_assoc();
        $display_time = showCustomerTimeSimple($purchase['purchase_time']);
        
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Purchase ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Purchase Time</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Ticket Type</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Total</th>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $purchase['purchaseid'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>" . $display_time . "</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $purchase['ticket_type'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>$" . $purchase['total'] . "</td>";
        echo "</tr>";
        
        echo "</table>";
        echo "<p><strong>UTC in Database:</strong> " . $purchase['purchase_time'] . "</p>";
        echo "<p><strong>Customer Display:</strong> " . $display_time . "</p>";
    } else {
        echo "<p>No purchase history found for demonstration.</p>";
    }
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<hr>";
echo "<h2>🌍 Customer Experience by Country</h2>";

// Simulate different customer experiences
$customer_scenarios = [
    'SG' => ['country' => 'Singapore', 'timezone' => 'Asia/Singapore'],
    'TH' => ['country' => 'Thailand', 'timezone' => 'Asia/Bangkok'],
    'JP' => ['country' => 'Japan', 'timezone' => 'Asia/Tokyo'],
    'US' => ['country' => 'United States', 'timezone' => 'America/New_York'],
    'GB' => ['country' => 'United Kingdom', 'timezone' => 'Europe/London']
];

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🌍 How Different Customers See the Same Time:</h3>";
echo "<p><strong>UTC Time in Database:</strong> $test_utc_time</p>";

echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Customer Country</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Timezone</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>What They See</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Timezone Indicator</th>";
echo "</tr>";

foreach ($customer_scenarios as $code => $scenario) {
    $customer_display = UTCTimeHelper::formatForDisplay($test_utc_time, $scenario['timezone'], 'Y-m-d H:i');
    
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>" . $scenario['country'] . "</strong></td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $scenario['timezone'] . "</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>$customer_display</strong></td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>Times shown in your timezone: " . $scenario['timezone'] . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>✅ Implementation Status</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎉 Completed Updates:</h3>";
echo "<ol>";
echo "<li>✅ <strong>my-ticket.php:</strong> Shows ticket creation time in user's timezone</li>";
echo "<li>✅ <strong>purchase-history.php:</strong> Shows purchase time in user's timezone</li>";
echo "<li>✅ <strong>my-ticket-log.php:</strong> Already updated (shows log time in user's timezone)</li>";
echo "<li>✅ <strong>Timezone indicators:</strong> All pages show user's timezone</li>";
echo "<li>✅ <strong>JavaScript auto-detection:</strong> All pages detect timezone automatically</li>";
echo "<li>✅ <strong>Consistent experience:</strong> Same timezone display across all customer pages</li>";
echo "</ol>";

echo "<h4>🎯 Customer Benefits:</h4>";
echo "<ul>";
echo "<li><strong>Consistent:</strong> All customer pages show time in their timezone</li>";
echo "<li><strong>Clear:</strong> Timezone indicator shows what timezone is used</li>";
echo "<li><strong>Automatic:</strong> No setup required - detects timezone automatically</li>";
echo "<li><strong>Accurate:</strong> Based on country from Stripe billing address</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>🧪 How to Test</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Testing Steps:</h3>";
echo "<ol>";
echo "<li><strong>Login as customer:</strong> Use any customer account</li>";
echo "<li><strong>Test my-ticket.php:</strong> Check ticket creation times</li>";
echo "<li><strong>Test purchase-history.php:</strong> Check purchase times</li>";
echo "<li><strong>Test my-ticket-log.php:</strong> Check log times</li>";
echo "<li><strong>Verify timezone indicators:</strong> Should show user's timezone</li>";
echo "<li><strong>Check browser console:</strong> Should see timezone detection logs</li>";
echo "</ol>";

echo "<h4>🔍 What to Look For:</h4>";
echo "<ul>";
echo "<li><strong>Timezone indicators:</strong> 'Times shown in your timezone: Asia/Bangkok'</li>";
echo "<li><strong>Consistent times:</strong> All pages show same timezone</li>";
echo "<li><strong>Local time display:</strong> Times in user's local timezone</li>";
echo "<li><strong>Auto-detection:</strong> Timezone updates when detected</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; background: #28a745; color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🎉 ALL CUSTOMER PAGES UPDATED!</h2>";
echo "<p><strong>my-ticket.php and purchase-history.php now show time in user's timezone!</strong></p>";
echo "<p>Consistent timezone experience across all customer pages.</p>";
echo "</div>";

echo "<hr>";
echo "<h2>📋 Summary</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎯 Updated Customer Pages:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Page</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Time Field</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Function Used</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>my-ticket-log.php</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Log created_at</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>showCustomerTimeSimple()</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Already updated</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>my-ticket.php</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Ticket created_at</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>showCustomerTimeSimple()</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Just updated</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>purchase-history.php</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Purchase purchase_time</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>showCustomerTimeSimple()</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Just updated</td>";
echo "</tr>";

echo "</table>";

echo "<h4>🌍 Global Features:</h4>";
echo "<ul>";
echo "<li>✅ <strong>190+ countries supported</strong> worldwide</li>";
echo "<li>✅ <strong>Automatic timezone detection</strong> from Stripe country codes</li>";
echo "<li>✅ <strong>Consistent display</strong> across all customer pages</li>";
echo "<li>✅ <strong>Real-time updates</strong> when timezone is detected</li>";
echo "</ul>";
echo "</div>";

// Include the JavaScript for demonstration
echo "<script src='js/customer-timezone.js'></script>";
echo "<script>";
echo "// Demo: Show customer timezone detection";
echo "setTimeout(() => {";
echo "    if (window.CustomerTimezone) {";
echo "        const info = window.CustomerTimezone.getCustomerTimezoneInfo();";
echo "        console.log('Customer Pages Timezone Test Info:', info);";
echo "    }";
echo "}, 2000);";
echo "</script>";
?>
