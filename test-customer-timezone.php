<?php
/**
 * Test Customer Timezone in My Ticket Log
 * Verify that customer timezone functions work in front-end
 */

// Include the database connection and timezone helper
include('functions/server.php');

echo "<h1>🧪 Test Customer Timezone in My Ticket Log</h1>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>✅ Customer Timezone Test</h2>";
echo "<p><strong>Goal:</strong> Verify that front-end/my-ticket-log.php displays time in customer's timezone</p>";
echo "<p><strong>Updated:</strong> Uses showCustomerTimeSimple() function</p>";
echo "<p><strong>Features:</strong> Auto-detection, timezone indicator, JavaScript integration</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🔧 Changes Made to my-ticket-log.php</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📝 Code Changes:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Change</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Before</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>After</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Time Display</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>formatTimeForDisplay(\$log['created_at'], getUserTimezone(), 'Y-m-d H:i')</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeSimple(\$log['created_at'])</code></td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>JavaScript</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>No timezone detection</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Added customer-timezone.js</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Timezone Indicator</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>None</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Shows customer's timezone</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>🧪 Test Customer Functions</h2>";

// Test customer timezone functions
$test_utc_time = getCurrentUTC();

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📊 Customer Function Results:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Function</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Result</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Used In</th>";
echo "</tr>";

// Test getCustomerTimezone()
$customer_timezone = getCustomerTimezone();
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>getCustomerTimezone()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_timezone</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Timezone indicator</td>";
echo "</tr>";

// Test showCustomerTimeSimple()
$customer_time_simple = showCustomerTimeSimple($test_utc_time);
echo "<tr style='background: #d4edda;'>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeSimple()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_time_simple</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Date column in table</strong></td>";
echo "</tr>";

// Test showCustomerTime()
$customer_time = showCustomerTime($test_utc_time);
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTime()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_time</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Alternative format</td>";
echo "</tr>";

// Test showCustomerTimeRelative()
$past_time = date('Y-m-d H:i:s', strtotime($test_utc_time . ' -2 hours'));
$customer_relative = showCustomerTimeRelative($past_time);
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><code>showCustomerTimeRelative()</code></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$customer_relative</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Future enhancement</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>📊 Sample Ticket Log Data</h2>";

// Get some sample ticket logs to show the conversion
try {
    $sample_sql = "SELECT tl.*, u.username 
                   FROM ticket_logs tl 
                   LEFT JOIN user u ON tl.user_id = u.id 
                   ORDER BY tl.created_at DESC 
                   LIMIT 3";
    $sample_result = $conn->query($sample_sql);
    
    if ($sample_result && $sample_result->num_rows > 0) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📋 Sample Data (as shown in my-ticket-log.php):</h3>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>User</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>UTC (Database)</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Customer Display</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Action</th>";
        echo "</tr>";
        
        while ($log = $sample_result->fetch_assoc()) {
            $display_time = showCustomerTimeSimple($log['created_at']);
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($log['username'] ?? 'N/A') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $log['created_at'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>" . $display_time . "</strong></td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $log['action'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p>No ticket logs found for demonstration.</p>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error retrieving sample data:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🌐 JavaScript Integration</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📱 Auto-Detection Features:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Automatic timezone detection</strong> when page loads</li>";
echo "<li>✅ <strong>Timezone indicator</strong> shows customer's timezone</li>";
echo "<li>✅ <strong>Dynamic updates</strong> when timezone is detected</li>";
echo "<li>✅ <strong>Session storage</strong> remembers customer's timezone</li>";
echo "</ul>";

echo "<h4>🔧 JavaScript Files Added:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo htmlspecialchars('
<!-- Customer Timezone Detection -->
<script src="../js/customer-timezone.js"></script>

<!-- Update timezone display when detected -->
<script>
document.addEventListener("customerTimezoneDetected", function(event) {
    const timezoneDisplay = document.getElementById("customer-timezone-display");
    if (timezoneDisplay) {
        timezoneDisplay.textContent = event.detail.timezone;
    }
});
</script>
');
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h2>👤 Customer Experience</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎯 What Customers Will See:</h3>";

// Simulate different customer timezones
$timezones = [
    'Asia/Singapore' => 'Singapore Customer',
    'Asia/Bangkok' => 'Bangkok Customer', 
    'America/New_York' => 'New York Customer',
    'Europe/London' => 'London Customer'
];

echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Customer Location</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>UTC in Database</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>What They See</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Timezone Indicator</th>";
echo "</tr>";

foreach ($timezones as $timezone => $customer_type) {
    $sample_utc = '2024-12-20 08:17:00';
    $customer_display = UTCTimeHelper::formatForDisplay($sample_utc, $timezone, 'Y-m-d H:i');
    
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>$customer_type</strong></td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$sample_utc</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>$customer_display</strong></td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$timezone</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>✅ Implementation Status</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎉 Completed Updates:</h3>";
echo "<ol>";
echo "<li>✅ <strong>Updated my-ticket-log.php:</strong> Uses showCustomerTimeSimple()</li>";
echo "<li>✅ <strong>Added timezone indicator:</strong> Shows customer's timezone</li>";
echo "<li>✅ <strong>JavaScript integration:</strong> Auto-detection and updates</li>";
echo "<li>✅ <strong>Customer functions:</strong> All timezone functions available</li>";
echo "<li>✅ <strong>Session management:</strong> Remembers customer timezone</li>";
echo "</ol>";

echo "<h4>🎯 Customer Benefits:</h4>";
echo "<ul>";
echo "<li><strong>Automatic:</strong> No setup required</li>";
echo "<li><strong>Accurate:</strong> Always shows their local time</li>";
echo "<li><strong>Clear:</strong> Timezone indicator shows what timezone is used</li>";
echo "<li><strong>Consistent:</strong> Same experience across all pages</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h2>🧪 How to Test</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Testing Steps:</h3>";
echo "<ol>";
echo "<li><strong>Login as customer:</strong> Access front-end/my-ticket-log.php</li>";
echo "<li><strong>Check timezone indicator:</strong> Should show your timezone</li>";
echo "<li><strong>View ticket log times:</strong> Should show your local time</li>";
echo "<li><strong>Check browser console:</strong> Should see timezone detection logs</li>";
echo "<li><strong>Create a ticket:</strong> Verify new log shows correct time</li>";
echo "</ol>";

echo "<h4>🔍 What to Look For:</h4>";
echo "<ul>";
echo "<li><strong>Timezone indicator:</strong> 'Times shown in your timezone: Asia/Singapore'</li>";
echo "<li><strong>Date column:</strong> Shows time in Y-m-d H:i format in your timezone</li>";
echo "<li><strong>Console logs:</strong> 'Customer timezone detected: Asia/Singapore'</li>";
echo "<li><strong>Consistency:</strong> All times should be in your local timezone</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; background: #28a745; color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🎉 CUSTOMER TIMEZONE READY!</h2>";
echo "<p><strong>front-end/my-ticket-log.php now displays time in customer's timezone!</strong></p>";
echo "<p>Customers will automatically see times in their local timezone with clear indication.</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🚀 Next Steps</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Extend to Other Pages:</h3>";
echo "<p>Apply the same pattern to other customer pages:</p>";
echo "<ul>";
echo "<li><strong>front-end/my-tickets.php:</strong> Support ticket list</li>";
echo "<li><strong>front-end/ticket-detail.php:</strong> Individual ticket view</li>";
echo "<li><strong>front-end/purchase-history.php:</strong> Purchase timestamps</li>";
echo "<li><strong>front-end/profile.php:</strong> Account creation date</li>";
echo "</ul>";

echo "<h4>🔧 Quick Update Pattern:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// Replace time displays with:
echo showCustomerTime($row["created_at"]);        // User-friendly format
echo showCustomerTimeSimple($row["created_at"]);  // Simple Y-m-d H:i format
echo showCustomerTimeRelative($row["created_at"]); // "2 hours ago" format

// Add JavaScript:
<script src="../js/customer-timezone.js"></script>

// Add timezone indicator:
<small class="text-muted">
    Times shown in your timezone: <span id="customer-timezone-display"><?php echo getCustomerTimezone(); ?></span>
</small>
');
echo "</pre>";
echo "</div>";

// Include the JavaScript for demonstration
echo "<script src='js/customer-timezone.js'></script>";
echo "<script>";
echo "// Demo: Show customer timezone detection";
echo "setTimeout(() => {";
echo "    if (window.CustomerTimezone) {";
echo "        const info = window.CustomerTimezone.getCustomerTimezoneInfo();";
echo "        console.log('Customer Timezone Test Info:', info);";
echo "    }";
echo "}, 2000);";
echo "</script>";
?>
