<?php
/**
 * Test Timezone System
 * Verify that ticket_logs are saving in GMT+0 and displaying in user's timezone
 */

// Include the database connection and timezone helper
include('functions/server.php');

echo "<h1>🧪 Timezone System Test</h1>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>✅ Test Setup</h2>";
echo "<p><strong>Goal:</strong> Verify ticket_logs save in GMT+0 and display in user's timezone</p>";
echo "<p><strong>Server:</strong> MST timezone (your server)</p>";
echo "<p><strong>Expected:</strong> Database stores UTC, displays Singapore time</p>";
echo "</div>";

echo "<hr>";
echo "<h2>🔧 Current System Status</h2>";

// Test current timezone configuration
$php_timezone = date_default_timezone_get();
$server_time = date('Y-m-d H:i:s');
$utc_time = getCurrentUTC();
$singapore_time = formatTimeForDisplay($utc_time, 'Asia/Singapore', 'Y-m-d H:i:s');

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>⏰ Time Comparison:</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Source</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Time</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>PHP Timezone</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$php_timezone</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($php_timezone === 'UTC' ? '✅ UTC' : '⚠️ Not UTC') . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Server Time</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$server_time</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>ℹ️ Server local</td>";
echo "</tr>";

echo "<tr style='background: #d4edda;'>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>UTC Time (for DB)</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$utc_time</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ What gets saved</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Singapore Display</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>$singapore_time</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ What user sees</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h2>🗄️ Database Timezone Test</h2>";

// Test database timezone
try {
    $db_result = $conn->query("SELECT NOW() as db_time, UTC_TIMESTAMP() as utc_time, @@session.time_zone as session_tz");
    if ($db_result && $row = $db_result->fetch_assoc()) {
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📊 Database Time Information:</h3>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Database Function</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Result</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>NOW()</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['db_time'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>ℹ️ Database current time</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>UTC_TIMESTAMP()</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['utc_time'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Always UTC</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Session Timezone</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['session_tz'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($row['session_tz'] === '+00:00' ? '✅ UTC' : '⚠️ Not UTC') . "</td>";
        echo "</tr>";
        
        echo "</table>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Database Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>📝 Test Ticket Log Creation</h2>";

// Create a test ticket log entry
$test_user_id = 1; // Use a test user ID
$test_ticket_id = 999; // Use a test ticket ID
$test_description = "Test timezone system - " . date('Y-m-d H:i:s');
$test_utc_time = getCurrentUTC();

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🧪 Creating Test Log Entry:</h3>";
echo "<p><strong>UTC Time to Save:</strong> $test_utc_time</p>";
echo "<p><strong>Description:</strong> $test_description</p>";

try {
    // Insert test log with UTC time
    $sql = "INSERT INTO ticket_logs (ticket_id, action, description, user_id, ticket_type, amount, created_at)
            VALUES (?, 'success', ?, ?, 'test', '1', ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isis", $test_ticket_id, $test_description, $test_user_id, $test_utc_time);
    
    if ($stmt->execute()) {
        $log_id = $conn->insert_id;
        echo "<p>✅ <strong>Test log created successfully!</strong> ID: $log_id</p>";
        
        // Retrieve the log to verify
        $verify_sql = "SELECT * FROM ticket_logs WHERE id = ?";
        $verify_stmt = $conn->prepare($verify_sql);
        $verify_stmt->bind_param("i", $log_id);
        $verify_stmt->execute();
        $result = $verify_stmt->get_result();
        $log_data = $result->fetch_assoc();
        
        if ($log_data) {
            echo "<h4>📊 Verification Results:</h4>";
            echo "<table style='width: 100%; border-collapse: collapse;'>";
            echo "<tr style='background: #e9ecef;'>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>Field</th>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>Stored Value</th>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>Display Value</th>";
            echo "</tr>";
            
            $stored_time = $log_data['created_at'];
            $display_time = formatTimeForDisplay($stored_time, 'Asia/Singapore', 'Y-m-d H:i:s');
            $display_formatted = formatTimeForDisplay($stored_time, 'Asia/Singapore');
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Created At</strong></td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>$stored_time</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>$display_time</td>";
            echo "</tr>";
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Formatted Display</strong></td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>-</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>$display_formatted</td>";
            echo "</tr>";
            
            echo "</table>";
            
            // Calculate time difference
            $utc_dt = new DateTime($stored_time, new DateTimeZone('UTC'));
            $sg_dt = new DateTime($stored_time, new DateTimeZone('UTC'));
            $sg_dt->setTimezone(new DateTimeZone('Asia/Singapore'));
            
            $time_diff = $sg_dt->getOffset() / 3600;
            
            echo "<p><strong>Timezone Conversion:</strong> UTC → Singapore (+$time_diff hours)</p>";
            
            // Clean up test data
            $cleanup_sql = "DELETE FROM ticket_logs WHERE id = ?";
            $cleanup_stmt = $conn->prepare($cleanup_sql);
            $cleanup_stmt->bind_param("i", $log_id);
            $cleanup_stmt->execute();
            echo "<p>🧹 <strong>Test data cleaned up</strong></p>";
        }
    } else {
        echo "<p>❌ <strong>Failed to create test log:</strong> " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<hr>";
echo "<h2>📋 Recent Ticket Logs</h2>";

// Show recent ticket logs with timezone conversion
try {
    $recent_sql = "SELECT tl.*, u.username 
                   FROM ticket_logs tl 
                   LEFT JOIN user u ON tl.user_id = u.id 
                   ORDER BY tl.created_at DESC 
                   LIMIT 5";
    $recent_result = $conn->query($recent_sql);
    
    if ($recent_result && $recent_result->num_rows > 0) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📊 Recent Logs (with timezone conversion):</h3>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>User</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Action</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>UTC (Stored)</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Singapore (Display)</th>";
        echo "</tr>";
        
        while ($log = $recent_result->fetch_assoc()) {
            $display_time = formatTimeForDisplay($log['created_at'], 'Asia/Singapore', 'Y-m-d H:i:s');
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $log['id'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($log['username'] ?? 'N/A') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $log['action'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $log['created_at'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $display_time . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p>No recent ticket logs found.</p>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Error retrieving logs:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>✅ Test Results Summary</h2>";

$issues = [];
$successes = [];

// Check PHP timezone
if ($php_timezone === 'UTC') {
    $successes[] = "PHP timezone set to UTC";
} else {
    $issues[] = "PHP timezone is $php_timezone (should be UTC)";
}

// Check if helper functions work
try {
    $test_utc = getCurrentUTC();
    $test_convert = formatTimeForDisplay($test_utc, 'Asia/Singapore');
    $successes[] = "Timezone helper functions working";
} catch (Exception $e) {
    $issues[] = "Timezone helper functions error: " . $e->getMessage();
}

// Check database session timezone
if (isset($row['session_tz']) && $row['session_tz'] === '+00:00') {
    $successes[] = "Database session timezone set to UTC";
} else {
    $issues[] = "Database session timezone not UTC";
}

echo "<div style='background: " . (empty($issues) ? '#d4edda' : '#fff3cd') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>" . (empty($issues) ? '🎉 All Tests Passed!' : '⚠️ Issues Found') . "</h3>";

if (!empty($successes)) {
    echo "<h4>✅ Working Correctly:</h4>";
    echo "<ul>";
    foreach ($successes as $success) {
        echo "<li>$success</li>";
    }
    echo "</ul>";
}

if (!empty($issues)) {
    echo "<h4>❌ Issues to Fix:</h4>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
}

echo "</div>";

echo "<hr>";
echo "<h2>🚀 Next Steps</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 Implementation Status:</h3>";
echo "<ol>";
echo "<li>✅ <strong>Timezone helper created:</strong> functions/timezone-helper.php</li>";
echo "<li>✅ <strong>Server.php updated:</strong> Includes timezone helper</li>";
echo "<li>✅ <strong>Ticket log creation updated:</strong> Uses getCurrentUTC()</li>";
echo "<li>✅ <strong>Display functions updated:</strong> Uses formatTimeForDisplay()</li>";
echo "<li>🧪 <strong>Testing:</strong> This test file</li>";
echo "</ol>";

echo "<h4>🎯 What This Achieves:</h4>";
echo "<ul>";
echo "<li><strong>Server Storage:</strong> All ticket logs saved in GMT+0 (UTC)</li>";
echo "<li><strong>User Display:</strong> Times shown in user's timezone (Singapore)</li>";
echo "<li><strong>Consistency:</strong> Same behavior on localhost and server</li>";
echo "<li><strong>No SUPER privileges needed:</strong> Pure PHP solution</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; background: #28a745; color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🎉 TIMEZONE SYSTEM IMPLEMENTED!</h2>";
echo "<p><strong>Your ticket_logs now save in GMT+0 and display in user's timezone!</strong></p>";
echo "<p>Test by creating a ticket and checking the logs in admin panel.</p>";
echo "</div>";
?>
